@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}
a{
    text-decoration: none;
    color: black;
}
.container{
    width: 100%;
    /* height: 100vh;    */
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
}
nav{
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 50px;
    flex-direction: row;
}
.nav-links{
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    list-style: none;
    gap: 2rem;
}
.nav-links ul li a{
    text-decoration: none;
}
.logo{
    width: 100px;
    height: 100px;
    padding: 1rem;
}
.logo img{
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Dropdown Styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dropdown-toggle::after {
    content: '';
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid black;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.dropdown-toggle:hover {
    color: #B9224B;
}

.dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg) scale(1.1);
    border-top-color: #B9224B;
    filter: drop-shadow(0 2px 4px rgba(185, 34, 75, 0.4));
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    min-width: 220px;
    box-shadow:
        0 20px 40px rgba(185, 34, 75, 0.1),
        0 8px 25px rgba(0, 0, 0, 0.08);
    border-radius: 16px;
    padding: 12px 0;
    list-style: none;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu li a {
    display: block;
    padding: 14px 24px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 12px;
    margin: 2px 12px;
    font-weight: 500;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.dropdown-menu li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    /* background: linear-gradient(90deg, transparent, rgba(185, 34, 75, 0.1), transparent); */
    transition: left 0.5s ease;
}

.dropdown-menu li a:hover {
    /* background: linear-gradient(135deg, rgba(185, 34, 75, 0.08), rgba(185, 34, 75, 0.04)); */
    color: #B9224B;
    /* transform: translateX(8px) scale(1.02);
    font-weight: 600; */
    /* box-shadow: 0 4px 12px rgba(185, 34, 75, 0.15); */
}

.dropdown-menu li a:hover::before {
    left: 100%;
}

/* Enhanced Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 12px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    width: 50px;
    height: 50px;
    justify-content: center;
    align-items: center;
}

.mobile-menu-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(185, 34, 75, 0.2), transparent);
    transition: left 0.6s ease;
}

.mobile-menu-btn:hover::before {
    left: 100%;
}

.mobile-menu-btn:hover {
    background: rgba(185, 34, 75, 0.05);
}

/* Hamburger Icons Only */
.hamburger-icon, .close-icon {
    position: absolute;
    font-size: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: #333;
    top: 50%;
    left: 50%;
    transform-origin: center;
}

.hamburger-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
}

.close-icon {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(180deg);
    color: #B9224B;
}

/* Mobile Menu Button Hover & Active States */
.mobile-menu-btn:hover {
    background: linear-gradient(135deg, rgba(185, 34, 75, 0.1), rgba(185, 34, 75, 0.05));
    box-shadow: 0 6px 25px rgba(185, 34, 75, 0.2);
    transform: translateY(-2px);
}

/* Mobile Menu Button Hover & Active States - Icons Only */
.mobile-menu-btn:hover .hamburger-icon {
    color: #B9224B;
    transform: translate(-50%, -50%) scale(1.1) rotate(0deg);
}

.mobile-menu-btn.active {
    background: linear-gradient(135deg, rgba(185, 34, 75, 0.15), rgba(185, 34, 75, 0.1));
    box-shadow: 0 8px 30px rgba(185, 34, 75, 0.3);
}

.mobile-menu-btn.active .hamburger-icon {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(-180deg);
}

.mobile-menu-btn.active .close-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0);
    backdrop-filter: blur(0px);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(15px);
}

/* Mobile Menu */
.mobile-menu {
    position: absolute;
    top: 0;
    right: 0;
    width: 320px;
    height: 100vh;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.98));
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    padding: 0;
    transform: translateX(100%) scale(0.9);
    transition: all 0.7s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    overflow-y: auto;
    box-shadow:
        -20px 0 60px rgba(185, 34, 75, 0.1),
        -10px 0 30px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(185, 34, 75, 0.05);
    border-left: 2px solid rgba(185, 34, 75, 0.1);
}

.mobile-menu-overlay.active .mobile-menu {
    transform: translateX(0) scale(1);
    box-shadow:
        -30px 0 80px rgba(185, 34, 75, 0.15),
        -15px 0 40px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(185, 34, 75, 0.1);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 15px;
    border-bottom: 1px solid rgba(185, 34, 75, 0.1);
}

.mobile-logo {
    width: 60px;
    height: 60px;
}

.mobile-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.mobile-close-btn {
    background: none;
    border: none;
    font-size: 32px;
    color: #333;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-close-btn:hover {
    background: rgba(185, 34, 75, 0.1);
    color: #B9224B;
    transform: rotate(90deg);
}

/* Mobile Navigation Links */
.mobile-nav-links {
    list-style: none;
    padding: 20px 0;
    margin: 0;
}

.mobile-nav-links > li {
    margin: 0;
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay.active .mobile-nav-links > li {
    opacity: 1;
    transform: translateX(0);
}

.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(1) { transition-delay: 0.1s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(2) { transition-delay: 0.15s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(3) { transition-delay: 0.2s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(4) { transition-delay: 0.25s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(5) { transition-delay: 0.3s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(6) { transition-delay: 0.35s; }
.mobile-menu-overlay.active .mobile-nav-links > li:nth-child(7) { transition-delay: 0.4s; }

.mobile-nav-links > li > a {
    display: block;
    padding: 18px 24px;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.mobile-nav-links > li > a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(185, 34, 75, 0.1), transparent);
    transition: left 0.6s ease;
}

.mobile-nav-links > li > a:hover::before {
    left: 100%;
    
}

.mobile-nav-links > li > a:hover {
    background: linear-gradient(135deg, rgba(185, 34, 75, 0.08), rgba(185, 34, 75, 0.04));
    color: #B9224B;
    border-left-color: #B9224B;
    transform: translateX(12px) scale(1.02);
    box-shadow: 0 4px 15px rgba(185, 34, 75, 0.1);
}

/* Mobile Dropdown */
.mobile-dropdown {
    position: relative;
}

.mobile-dropdown-toggle {
    position: relative;
    padding-right: 50px !important;
}

.mobile-dropdown-toggle::after {
    content: '';
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #666;
    transition: all 0.3s ease;
}

.mobile-dropdown.active .mobile-dropdown-toggle::after {
    transform: translateY(-50%) rotate(180deg);
    border-top-color: #B9224B;
}

.mobile-dropdown-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    background: rgba(185, 34, 75, 0.02);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-dropdown.active .mobile-dropdown-menu {
    max-height: 300px;
    padding: 8px 0;
}

.mobile-dropdown-menu li a {
    display: block;
    padding: 12px 24px 12px 48px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.mobile-dropdown-menu li a::before {
    content: '•';
    position: absolute;
    left: 36px;
    color: #B9224B;
    font-weight: bold;
}

.mobile-dropdown-menu li a:hover {
    background: rgba(185, 34, 75, 0.08);
    color: #B9224B;
    transform: translateX(8px);
}

/* Compact Mobile Connect Section */
.mobile-connect-section {
    padding: 15px 20px 18px;
    border-top: 1px solid rgba(185, 34, 75, 0.1);
    margin-top: 15px;
    background: linear-gradient(135deg, rgba(185, 34, 75, 0.02), rgba(185, 34, 75, 0.05));
}

.mobile-connect-btn {
    margin-bottom: 15px;
}

/* Compact Enhanced Connect Button */
.connect-btn-enhanced {
    display: block !important;
    background: linear-gradient(135deg, #B9224B, #d63384) !important;
    color: white !important;
    padding: 12px 20px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    border-radius: 12px !important;
    box-shadow:
        0 6px 20px rgba(185, 34, 75, 0.25),
        0 2px 10px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.6px !important;
    text-align: center !important;
    position: relative !important;
    overflow: hidden !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.connect-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.connect-btn-enhanced:hover::before {
    left: 100%;
}

.connect-btn-enhanced:hover {
    background: linear-gradient(135deg, #d63384, #ff6b9d) !important;
    transform: translateY(-4px) scale(1.02) !important;
    box-shadow:
        0 15px 40px rgba(185, 34, 75, 0.4),
        0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
}

.connect-btn-enhanced .me-2 {
    margin-right: 8px;
}

/* Fallback for regular mobile connect button */
.mobile-connect-btn a:not(.connect-btn-enhanced) {
    display: block;
    background: linear-gradient(135deg, #B9224B, #d63384);
    color: white;
    padding: 15px 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(185, 34, 75, 0.3);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
}

.mobile-connect-btn a:not(.connect-btn-enhanced):hover {
    background: linear-gradient(135deg, #d63384, #B9224B);
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(185, 34, 75, 0.4);
}

/* Compact Mobile Social Links */
.mobile-social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Compact Enhanced Social Links */
.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border: 1px solid rgba(185, 34, 75, 0.15);
    border-radius: 50%;
    color: #666;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.08),
        0 1px 6px rgba(185, 34, 75, 0.08);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.social-link:hover::before {
    left: 100%;
}

.social-link:hover {
    color: white !important;
    transform: translateY(-2px) scale(1.08);
    border-color: transparent;
    box-shadow:
        0 8px 25px rgba(185, 34, 75, 0.3),
        0 4px 15px rgba(0, 0, 0, 0.12);
}

/* Individual Social Platform Colors */
.social-link[aria-label="Facebook"]:hover {
    background: linear-gradient(135deg, #1877f2, #42a5f5);
}

.social-link[aria-label="Instagram"]:hover {
    background: linear-gradient(135deg, #e4405f, #fd1d1d, #fcb045);
}

.social-link[aria-label="LinkedIn"]:hover {
    background: linear-gradient(135deg, #0077b5, #00a0dc);
}

/* Social Link Animation Delays */
.mobile-social-links .social-link:nth-child(1) {
    animation-delay: 0.1s;
}

.mobile-social-links .social-link:nth-child(2) {
    animation-delay: 0.2s;
}

.mobile-social-links .social-link:nth-child(3) {
    animation-delay: 0.3s;
}

/* Responsive Design */

/* iPad Air and Large Tablets (769px - 1024px) - Use Mobile Menu */
@media screen and (min-width: 769px) and (max-width: 1024px) {
    nav {
        padding: 0 40px;
    }

    .nav-links {
        display: none;
    }

    /* Hide desktop Let's Connect button on tablets */
    .btn {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .logo {
        width: 112px;
        height: 112px;
    }

    .mobile-menu {
        width: 400px;
    }

    .mobile-menu-btn {
        width: 52px;
        height: 52px;
    }

    .hamburger-icon, .close-icon {
        font-size: 22px;
    }
}

/* Tablets and Mobile Landscape (481px - 768px) */
@media screen and (min-width: 481px) and (max-width: 768px) {
    nav {
        padding: 0 25px;
        height: 75px;
    }

    .nav-links {
        display: none;
    }

    /* Hide desktop Let's Connect button on mobile */
    .btn {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .logo {
        width: 85px;
        height: 85px;
    }

    .mobile-menu {
        width: 350px;
    }
}

/* Mobile Devices (max-width: 768px) */
@media screen and (max-width: 768px) {
    nav {
        padding: 0 20px;
    }

    .nav-links {
        display: none;
    }

    /* Hide desktop Let's Connect button on mobile */
    .btn {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .logo {
        width: 110px;
        height: 110px;
    }
}

/* Small Mobile Devices (max-width: 480px) */
@media screen and (max-width: 480px) {
    nav {
        padding: 0 15px;
        height: 70px;
    }

    .logo {
        width: 95px;
        height: 95px;
    }

    .mobile-menu {
        width: 100%;
    }

    .container {
        padding: 0;
    }

    .mobile-menu-btn {
        width: 45px;
        height: 45px;
        padding: 10px;
    }

    .hamburger-icon, .close-icon {
        font-size: 18px;
    }
}

/* Extra Small Mobile Devices (max-width: 360px) */
@media screen and (max-width: 360px) {
    nav {
        padding: 0 12px;
        height: 65px;
    }

    .logo {
        width: 95px;
        height: 95px;
    }

    .mobile-menu-btn {
        width: 42px;
        height: 42px;
        padding: 8px;
    }

    .hamburger-icon, .close-icon {
        font-size: 16px;
    }

    .mobile-menu-header {
        padding: 6px 9px;
    }

    .mobile-logo {
        width: 65px;
        height: 65px;
    }

    .mobile-connect-section {
        padding: 12px 15px 15px;
    }

    .connect-btn-enhanced {
        padding: 10px 16px !important;
        font-size: 13px !important;
    }

    .social-link {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
}

/* Desktop and Above (min-width: 1025px) */
@media screen and (min-width: 1025px) {
    .mobile-menu-btn,
    .mobile-menu-overlay {
        display: none !important;
    }

    .btn {
        display: block;
    }

    .nav-links {
        display: flex;
    }
}

/* Large Desktop Styles (min-width: 1200px) */
@media screen and (min-width: 1200px) {
    nav {
        padding: 0 80px;
    }

    .nav-links {
        gap: 3rem;
    }

    .logo {
        width: 120px;
        height: 120px;
    }

    .btn a {
        padding: 12px 24px;
        font-size: 16px;
    }

    .dropdown-menu {
        min-width: 240px;
    }
}

/* Extra Large Screens (min-width: 1440px) */
@media screen and (min-width: 1440px) {
    nav {
        padding: 0 100px;
    }

    .nav-links {
        gap: 4rem;
        font-size: 17px;
    }

    .logo {
        width: 130px;
        height: 130px;
    }

    .dropdown-menu {
        min-width: 260px;
    }
}

/* Ultra Wide Screens (min-width: 1920px) */
@media screen and (min-width: 1920px) {
    nav {
        padding: 0 120px;
        height: 90px;
    }

    .nav-links {
        gap: 5rem;
        font-size: 18px;
    }

    .logo {
        width: 140px;
        height: 140px;
    }

    .btn a {
        padding: 14px 28px;
        font-size: 17px;
    }
}

/* Desktop Connect Button */
.btn a{
    color: white;
    background-color: #B9224B;
    padding: 10px 20px;
    border-radius: 1.2rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    min-height: 44px; /* Minimum touch target size */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn a:hover{
    background-color: #d12a57;
    color: #fdfdfd;
}

/* Additional Responsive Fixes */

/* Ensure proper touch targets on mobile */
@media screen and (max-width: 768px) {
    .mobile-nav-links > li > a {
        min-height: 48px; /* Recommended touch target size */
        display: flex;
        align-items: center;
    }

    .mobile-dropdown-menu li a {
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .social-link {
        min-width: 44px;
        min-height: 44px;
    }

    .mobile-close-btn {
        min-width: 44px;
        min-height: 44px;
    }
}

/* Landscape orientation adjustments */
@media screen and (max-height: 500px) and (orientation: landscape) {
    .mobile-menu {
        padding-top: 10px;
    }

    .mobile-menu-header {
        padding: 10px 20px;
    }

    .mobile-nav-links {
        padding: 10px 0;
    }

    .mobile-nav-links > li > a {
        padding: 12px 24px;
    }

    .mobile-connect-section {
        padding: 10px 15px 12px;
    }
}

/* High DPI displays */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
    .logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===== HERO SECTION ===== */
.hero-section {
    /* Seamless unified background with your color #771630 */
    background: #771630;
    min-height: 85vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 40px 0 120px;
}

.hero-container {
    max-width: 1300px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    color: white;
}

.hero-text {
    margin-bottom: 25px;
}

/* Professional Badge */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(20px);
    color: rgba(255, 255, 255, 0.9);
}

.hero-badge i {
    color: #ffffff;
    font-size: 0.8rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 15px;
    overflow: visible; /* Changed from hidden to visible to prevent text cutting */
}

.hero-title-line {
    display: block;
    transform: translateY(100%);
    opacity: 0;
}

.hero-title-highlight {
    background: linear-gradient(45deg, #ffffff, #f8fafc, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-title-highlight::after {
    content: '';
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 0;
    height: 4px;
    background: linear-gradient(90deg, #ffffff, #f8fafc);
    border-radius: 2px;
}

/* Rotating Text Animation */
.rotating-text {
    position: relative;
    display: inline-block;
    min-width: 450px; /* Significantly increased to prevent any word cutting */
    width: auto; /* Allow container to expand as needed */
    height: 1.2em;
    vertical-align: top;
    overflow: visible; /* Ensure content is not clipped */
}

.rotate-word {
    position: absolute;
    top: 0;
    left: 0;
    width: auto; /* Changed from 100% to auto to prevent constraint */
    min-width: 100%; /* Ensure it takes at least full container width */
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    white-space: nowrap;
    overflow: visible; /* Ensure text is not clipped */
    background: linear-gradient(45deg, #ffffff, #f8fafc, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

/* Add icons and patterns to each rotating word */
.rotate-word::before {
    content: '';
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.8;
    transition: all 0.6s ease;
}

/* Property word - House icon */
.rotate-word:nth-child(1)::before {
    content: '🏠';
    font-size: 24px;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Investment word - Chart icon */
.rotate-word:nth-child(2)::before {
    content: '📈';
    font-size: 24px;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Real Estate word - Building icon */
.rotate-word:nth-child(3)::before {
    content: '🏢';
    font-size: 24px;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dream Home word - Key icon */
.rotate-word:nth-child(4)::before {
    content: '🔑';
    font-size: 24px;
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rotate-word.active {
    opacity: 1;
    transform: translateY(0);
    animation: wordGlow 2s ease-in-out infinite alternate;
}

.rotate-word.active::before {
    animation: iconBounce 2s ease-in-out infinite;
}

.rotate-word.exit {
    opacity: 0;
    transform: translateY(-100%);
}

/* Add background pattern for each word type */
.rotate-word.active::after {
    content: '';
    position: absolute;
    top: -10px;
    left: -50px;
    right: -20px;
    bottom: -10px;
    background: linear-gradient(90deg,
        transparent,
        rgba(185, 34, 75, 0.1),
        transparent
    );
    border-radius: 15px;
    z-index: -1;
    animation: patternFlow 3s ease-in-out infinite;
}

/* Specific patterns for each word */
.rotate-word:nth-child(1).active::after {
    background: linear-gradient(90deg,
        transparent,
        rgba(76, 175, 80, 0.15), /* Green for Property */
        transparent
    );
}

.rotate-word:nth-child(2).active::after {
    background: linear-gradient(90deg,
        transparent,
        rgba(33, 150, 243, 0.15), /* Blue for Investment */
        transparent
    );
}

.rotate-word:nth-child(3).active::after {
    background: linear-gradient(90deg,
        transparent,
        rgba(185, 34, 75, 0.15), /* Brand color for Real Estate */
        transparent
    );
}

.rotate-word:nth-child(4).active::after {
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 193, 7, 0.15), /* Gold for Dream Home */
        transparent
    );
}

.hero-description {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(30px);
    color: rgba(255, 255, 255, 0.9);
    max-width: 450px;
}

.hero-desc-line {
    display: block;
    opacity: 0;
    transform: translateX(-20px);
    margin-bottom: 4px;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.hero-btn {
    padding: 16px 32px;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    transform: translateY(50px);
    opacity: 0;
}

.hero-btn-primary {
    background: linear-gradient(45deg, #ffffff, #f8fafc);
    color: #B9224B;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.hero-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #f8fafc, #e2e8f0);
}

.hero-btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.hero-stats {
    display: flex;
    gap: 25px;
    margin-top: 25px;
}

.hero-stat {
    text-align: center;
    opacity: 0;
    transform: translateY(30px);
}

.hero-stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: white;
    margin-bottom: 4px;
}

.hero-stat-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Hero Visual Elements */
.hero-visual {
    position: relative;
    height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.18);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 25px;
    padding: 28px;
    display: flex;
    align-items: center;
    gap: 18px;
    color: white;
    font-weight: 600;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 10px 25px rgba(119, 22, 48, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 30px rgba(119, 22, 48, 0.1);
    opacity: 0;
    transform: scale(0.8);
    overflow: hidden;
}

.floating-card i {
    font-size: 2rem;
    color: #ffffff;
}

.floating-card-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0.5s;
}

.floating-card-2 {
    top: 50%;
    right: 20%;
    animation-delay: 1s;
}

.floating-card-3 {
    bottom: 20%;
    left: 30%;
    animation-delay: 1.5s;
}

.floating-card-4 {
    bottom: 35%;
    right: 15%;
    animation-delay: 2s;
}

/* Wave Bottom - Brand color background for stunning hero section */
.hero-wave-bottom {
    position: absolute;
    bottom: -50px; /* Extended further down to ensure complete wave visibility */
    left: 0;
    width: 100%;
    height: 455px; /* Significantly increased height for complete wave */
    overflow: visible; /* Keep visible so wave doesn't get cut */
    z-index: 1;
    /* Brand color background for stunning look that matches hero section */
    /* background: linear-gradient(180deg, #771630 0%, #B9224B 100%); */
    /* Remove all borders, shadows, and effects that create separation */
    border: none;
    box-shadow: none;
}

.wave-image {
    width: 100%;
    height: auto; /* Let image maintain aspect ratio */
    min-height: 390px; /* Match container height */
    max-height: none; /* Remove any height restrictions */
    object-fit: contain; /* Use contain to show complete image */
    object-position: center bottom; /* Position at bottom to show full wave */
    display: block;
    min-width: 100%;
    position: relative;
    z-index: 2;

    /* Enhanced filter to blend beautifully with brand color gradient background */
    filter:
        hue-rotate(-15deg)
        saturate(1.2)
        brightness(0.9)
        contrast(1.2);

    /* Normal blend mode to prevent color bleeding into white section below */
    mix-blend-mode: normal;
    opacity: 0.9;
}

/* Remove all overlay effects for seamless look */

/* Keyframe animations */
@keyframes waveGradient {
    0%, 100% {
        background-position: 0% 0%;
        opacity: 0.8;
    }
    50% {
        background-position: 100% 100%;
        opacity: 1;
    }
}

@keyframes sparkle {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1);
        opacity: 0.7;
    }
    25% {
        transform: translateX(-10px) translateY(-5px) scale(1.1);
        opacity: 0.9;
    }
    50% {
        transform: translateX(10px) translateY(5px) scale(0.9);
        opacity: 0.8;
    }
    75% {
        transform: translateX(-5px) translateY(10px) scale(1.05);
        opacity: 0.6;
    }
}

/* Animated Background Elements */
.hero-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    opacity: 0.6;
}

.bg-circle-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: 10%;
    animation: float 6s ease-in-out infinite;
}

.bg-circle-2 {
    width: 200px;
    height: 200px;
    bottom: 30%;
    left: 5%;
    animation: float 8s ease-in-out infinite reverse;
}

.bg-circle-3 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 30%;
    animation: float 10s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Hero Section Responsive Design */

/* Large Desktop */
@media screen and (min-width: 1440px) {
    .hero-container {
        max-width: 1600px;
        gap: 100px;
    }

    .hero-title {
        font-size: 5.5rem;
    }

    .hero-description {
        font-size: 1.4rem;
    }
}

/* Desktop */
@media screen and (max-width: 1200px) {
    .hero-container {
        gap: 60px;
        padding: 0 30px;
    }

    .hero-title {
        font-size: 4rem;
    }
}

/* Tablet */
@media screen and (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
        padding: 0 30px;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .hero-visual {
        height: 400px;
    }

    .hero-stats {
        justify-content: center;
    }

    .floating-card {
        padding: 20px;
    }

    .floating-card i {
        font-size: 1.5rem;
    }
}

/* Mobile */
@media screen and (max-width: 768px) {
    .hero-section {
        padding: 100px 0 80px;
        min-height: 80vh;
    }

    .hero-container {
        padding: 0 20px;
        gap: 30px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .rotating-text {
        min-width: 350px; /* Significantly increased for mobile to prevent word cutting */
        height: 1.1em;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .hero-btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .hero-stats {
        gap: 30px;
        margin-top: 40px;
    }

    .hero-stat-number {
        font-size: 2.5rem;
    }

    .hero-visual {
        height: 300px;
    }

    .floating-card {
        padding: 16px;
        font-size: 0.9rem;
    }

    .floating-card i {
        font-size: 1.2rem;
    }

    .hero-wave-bottom {
        height: 250px; /* Further increased to prevent cutting */
        bottom: -30px; /* Extended down for mobile */
    }

    .next-section {
        padding: 60px 0;
    }

    .next-section .container {
        padding: 0 20px;
    }

    .next-section h2 {
        font-size: 2rem;
    }
}

/* Small Mobile */
@media screen and (max-width: 480px) {
    .hero-title {
        font-size: 2.2rem;
    }

    .rotating-text {
        min-width: 300px; /* Significantly increased for small mobile to prevent word cutting */
        height: 1em;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 20px;
    }

    .hero-stat-number {
        font-size: 2rem;
    }

    .floating-card {
        padding: 12px;
        font-size: 0.8rem;
        gap: 12px;
    }

    .hero-wave-bottom {
        height: 220px; /* Further increased to prevent cutting */
        bottom: -25px; /* Extended down for small mobile */
    }
}

/* Next Section - To show wave transition */
.next-section {
    background: #EDE3E6 !important;
    padding: 40px 0;
    position: relative;
    z-index: 3;

    /* Ensure seamless transition without any visible lines */
    border: none;
}

.next-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
    text-align: center;
}

.next-section h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 20px;
}

.next-section p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== PERFECT WAVE ANIMATIONS ===== */

/* Perfect Wave Flow Animation for Hero Section */
@keyframes perfectWaveFlow {
    0%, 100% {
        background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
    }
    25% {
        background-position: 10% 5%, 25% 25%, -25% -25%, 15% 10%, -15% -10%, 5% 2%;
    }
    50% {
        background-position: 20% 10%, 50% 50%, -50% -50%, 30% 20%, -30% -20%, 10% 5%;
    }
    75% {
        background-position: 10% 5%, 25% 25%, -25% -25%, 15% 10%, -15% -10%, 5% 2%;
    }
}

/* Wave Bottom Flow Animation */
@keyframes waveBottomFlow {
    0%, 100% {
        transform: translateX(0) scale(1);
        filter: hue-rotate(0deg);
    }
    33% {
        transform: translateX(-2px) scale(1.01);
        filter: hue-rotate(5deg);
    }
    66% {
        transform: translateX(2px) scale(0.99);
        filter: hue-rotate(-5deg);
    }
}

/* Wave Image Flow Animation */
@keyframes waveImageFlow {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1);
    }
    25% {
        transform: translateX(-3px) translateY(-1px) scale(1.005);
    }
    50% {
        transform: translateX(0) translateY(-2px) scale(1.01);
    }
    75% {
        transform: translateX(3px) translateY(-1px) scale(1.005);
    }
}
